package com.investment.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * Request model for Bollinger Band calculation operations.
 */
@Schema(description = "Request parameters for calculating Bollinger Band technical indicators")
public class BollingerBandRequest {

    /**
     * Enumeration for different calculation modes.
     */
    @Schema(description = "Calculation mode for Bollinger Band processing")
    public enum CalculationMode {
        @Schema(description = "Calculate only for new data points that haven't been processed yet")
        INCREMENTAL,

        @Schema(description = "Calculate from the beginning for all data points, overwriting existing calculations")
        FULL_RECALCULATION,

        @Schema(description = "Skip symbols that already have Bollinger Band data (legacy mode)")
        SKIP_EXISTING
    }
    
    @Schema(description = "Number of periods for moving average calculation", 
            example = "20", 
            minimum = "5", 
            maximum = "200")
    @Min(value = 5, message = "Period must be at least 5")
    @Max(value = 200, message = "Period must not exceed 200")
    private int period = 20;
    
    @Schema(description = "Standard deviation multiplier for upper and lower bands", 
            example = "2.0", 
            minimum = "0.5", 
            maximum = "5.0")
    @Min(value = 1, message = "Standard deviation multiplier must be at least 0.5")
    @Max(value = 10, message = "Standard deviation multiplier must not exceed 5.0")
    private double stdDevMultiplier = 2.0;
    
    @Schema(description = "If true, only validate and report without updating data", 
            example = "false")
    private boolean dryRun = false;
    
    @Schema(description = "Maximum number of symbols to process (0 = no limit)", 
            example = "0", 
            minimum = "0", 
            maximum = "10000")
    @Min(value = 0, message = "Max symbols must be non-negative")
    @Max(value = 10000, message = "Max symbols must not exceed 10000")
    private int maxSymbols = 0;
    
    @Schema(description = "Minimum number of data points required for calculation", 
            example = "20", 
            minimum = "5", 
            maximum = "500")
    @Min(value = 5, message = "Minimum data points must be at least 5")
    @Max(value = 500, message = "Minimum data points must not exceed 500")
    private int minDataPoints = 20;
    
    @Schema(description = "Calculation mode: INCREMENTAL (process only new data), FULL_RECALCULATION (recalculate all data), or SKIP_EXISTING (skip symbols with existing data)",
            example = "INCREMENTAL")
    private CalculationMode calculationMode = CalculationMode.INCREMENTAL;

    @Schema(description = "If true, recalculate for symbols that already have Bollinger Band data (deprecated - use calculationMode instead)",
            example = "false")
    @Deprecated
    private boolean forceRecalculate = false;

    // Default constructor
    public BollingerBandRequest() {}

    // Constructor with common parameters
    public BollingerBandRequest(int period, double stdDevMultiplier, boolean dryRun) {
        this.period = period;
        this.stdDevMultiplier = stdDevMultiplier;
        this.dryRun = dryRun;
    }

    // Constructor with calculation mode
    public BollingerBandRequest(int period, double stdDevMultiplier, boolean dryRun, CalculationMode calculationMode) {
        this.period = period;
        this.stdDevMultiplier = stdDevMultiplier;
        this.dryRun = dryRun;
        this.calculationMode = calculationMode;
    }

    // Getters and setters
    public int getPeriod() {
        return period;
    }

    public void setPeriod(int period) {
        this.period = period;
    }

    public double getStdDevMultiplier() {
        return stdDevMultiplier;
    }

    public void setStdDevMultiplier(double stdDevMultiplier) {
        this.stdDevMultiplier = stdDevMultiplier;
    }

    public boolean isDryRun() {
        return dryRun;
    }

    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }

    public int getMaxSymbols() {
        return maxSymbols;
    }

    public void setMaxSymbols(int maxSymbols) {
        this.maxSymbols = maxSymbols;
    }

    public int getMinDataPoints() {
        return minDataPoints;
    }

    public void setMinDataPoints(int minDataPoints) {
        this.minDataPoints = minDataPoints;
    }

    public CalculationMode getCalculationMode() {
        return calculationMode;
    }

    public void setCalculationMode(CalculationMode calculationMode) {
        this.calculationMode = calculationMode;
    }

    @Deprecated
    public boolean isForceRecalculate() {
        return forceRecalculate;
    }

    @Deprecated
    public void setForceRecalculate(boolean forceRecalculate) {
        this.forceRecalculate = forceRecalculate;
        // For backward compatibility, map boolean to enum
        if (forceRecalculate) {
            this.calculationMode = CalculationMode.FULL_RECALCULATION;
        } else {
            this.calculationMode = CalculationMode.SKIP_EXISTING;
        }
    }

    @Override
    public String toString() {
        return "BollingerBandRequest{" +
                "period=" + period +
                ", stdDevMultiplier=" + stdDevMultiplier +
                ", dryRun=" + dryRun +
                ", maxSymbols=" + maxSymbols +
                ", minDataPoints=" + minDataPoints +
                ", calculationMode=" + calculationMode +
                ", forceRecalculate=" + forceRecalculate + " (deprecated)" +
                '}';
    }
}
